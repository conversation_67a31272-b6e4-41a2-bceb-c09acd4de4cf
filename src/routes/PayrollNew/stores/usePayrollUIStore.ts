import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface PayrollUIState {
  // Modal states
  showSettingsModal: boolean
  showExportModal: boolean

  // Actions
  setShowSettingsModal: (show: boolean) => void
  setShowExportModal: (show: boolean) => void
  openSettingsModal: () => void
  closeSettingsModal: () => void
  openExportModal: () => void
  closeExportModal: () => void
  reset: () => void
}

export const usePayrollUIStore = create<PayrollUIState>()(
  devtools(
    set => ({
      // Initial state
      showSettingsModal: false,
      showExportModal: false,

      // Actions
      setShowSettingsModal: show =>
        set({ showSettingsModal: show }, false, 'setShowSettingsModal'),
      setShowExportModal: show =>
        set({ showExportModal: show }, false, 'setShowExportModal'),
      openSettingsModal: () =>
        set({ showSettingsModal: true }, false, 'openSettingsModal'),
      closeSettingsModal: () =>
        set({ showSettingsModal: false }, false, 'closeSettingsModal'),
      openExportModal: () =>
        set({ showExportModal: true }, false, 'openExportModal'),
      closeExportModal: () =>
        set({ showExportModal: false }, false, 'closeExportModal'),

      reset: () =>
        set(
          {
            showSettingsModal: false,
            showExportModal: false
          },
          false,
          'reset'
        )
    }),
    {
      name: 'payroll-ui-store'
    }
  )
)
