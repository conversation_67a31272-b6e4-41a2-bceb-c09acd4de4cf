import { Moment } from 'moment'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

import { ShiftAnalysis } from 'utils/payroll/shiftStatusAnalysis'

import { AttendanceShift } from 'types/attendance'

interface TooltipState {
  showPlanned: boolean
  showClocked: boolean
  showOverlap: boolean
  showRole: boolean
  canShow: boolean
  showDeleteBreak: string | null
}

interface ShiftPopoverState {
  // Shift data
  activeShift: number | null
  unsavedChanges: { [shiftIndex: number]: Partial<AttendanceShift> }
  selectedRole: string | null

  // Time pickers
  roundedTimeStart: Moment | null
  roundedTimeEnd: Moment | null

  // Analysis
  hoursTableAnalysis: ShiftAnalysis | null

  // UI state
  tooltips: TooltipState
  showActivityPopover: boolean
  showDeleteShiftModal: boolean
  hasClaimed: boolean

  // Actions
  setActiveShift: (shift: number | null) => void
  updateLocalShift: (
    shiftIndex: number,
    updates: Partial<AttendanceShift>
  ) => void
  clearUnsavedChanges: () => void
  setSelectedRole: (role: string | null) => void
  setTimeStart: (time: Moment | null) => void
  setTimeEnd: (time: Moment | null) => void
  setAnalysis: (analysis: ShiftAnalysis | null) => void
  toggleTooltip: (
    type: keyof Omit<TooltipState, 'showDeleteBreak' | 'canShow'>
  ) => void
  setCanShowTooltip: (canShow: boolean) => void
  setDeleteBreakTooltip: (breakId: string | null) => void
  toggleActivityPopover: () => void
  toggleDeleteShiftModal: () => void
  setHasClaimed: (claimed: boolean) => void
  reset: () => void
}

const initialTooltipState: TooltipState = {
  showPlanned: true,
  showClocked: false,
  showOverlap: false,
  showRole: false,
  canShow: false,
  showDeleteBreak: null
}

export const useShiftPopoverStore = create<ShiftPopoverState>()(
  devtools(
    (set, get) => ({
      // Initial state
      activeShift: 0,
      unsavedChanges: {},
      selectedRole: null,
      roundedTimeStart: null,
      roundedTimeEnd: null,
      hoursTableAnalysis: null,
      tooltips: initialTooltipState,
      showActivityPopover: false,
      showDeleteShiftModal: false,
      hasClaimed: false,

      // Actions
      setActiveShift: shift =>
        set({ activeShift: shift }, false, 'setActiveShift'),

      updateLocalShift: (shiftIndex, updates) =>
        set(
          state => {
            const currentUnsaved = state.unsavedChanges[shiftIndex] || {}

            // Check if any values actually changed
            let hasChanges = false
            for (const [key, newValue] of Object.entries(updates)) {
              if (key === 'breaks') {
                const currentBreaks = currentUnsaved[key] || {}
                const newBreaks = newValue || {}
                if (
                  JSON.stringify(currentBreaks) !== JSON.stringify(newBreaks)
                ) {
                  hasChanges = true
                  break
                }
              } else if (
                currentUnsaved[key as keyof AttendanceShift] !== newValue
              ) {
                hasChanges = true
                break
              }
            }

            if (!hasChanges) return state

            return {
              unsavedChanges: {
                ...state.unsavedChanges,
                [shiftIndex]: { ...currentUnsaved, ...updates }
              }
            }
          },
          false,
          'updateLocalShift'
        ),

      clearUnsavedChanges: () =>
        set({ unsavedChanges: {} }, false, 'clearUnsavedChanges'),

      setSelectedRole: role =>
        set({ selectedRole: role }, false, 'setSelectedRole'),

      setTimeStart: time =>
        set({ roundedTimeStart: time }, false, 'setTimeStart'),

      setTimeEnd: time => set({ roundedTimeEnd: time }, false, 'setTimeEnd'),

      setAnalysis: analysis =>
        set({ hoursTableAnalysis: analysis }, false, 'setAnalysis'),

      toggleTooltip: type =>
        set(
          state => ({
            tooltips: {
              ...state.tooltips,
              [type]: !state.tooltips[type]
            }
          }),
          false,
          `toggleTooltip-${type}`
        ),

      setCanShowTooltip: canShow =>
        set(
          state => ({
            tooltips: { ...state.tooltips, canShow }
          }),
          false,
          'setCanShowTooltip'
        ),

      setDeleteBreakTooltip: breakId =>
        set(
          state => ({
            tooltips: { ...state.tooltips, showDeleteBreak: breakId }
          }),
          false,
          'setDeleteBreakTooltip'
        ),

      toggleActivityPopover: () =>
        set(
          state => ({
            showActivityPopover: !state.showActivityPopover
          }),
          false,
          'toggleActivityPopover'
        ),

      toggleDeleteShiftModal: () =>
        set(
          state => ({
            showDeleteShiftModal: !state.showDeleteShiftModal
          }),
          false,
          'toggleDeleteShiftModal'
        ),

      setHasClaimed: (claimed: boolean) =>
        set(
          {
            hasClaimed: claimed
          },
          false,
          'setHasClaimed'
        ),

      reset: () =>
        set(
          {
            activeShift: 0,
            unsavedChanges: {},
            selectedRole: null,
            roundedTimeStart: null,
            roundedTimeEnd: null,
            hoursTableAnalysis: null,
            tooltips: initialTooltipState,
            showActivityPopover: false,
            showDeleteShiftModal: false,
            hasClaimed: false
          },
          false,
          'reset'
        )
    }),
    {
      name: 'shift-popover-store' // Store name for devtools
    }
  )
)
