import dayjs from 'dayjs'
import { create } from 'zustand'

type CalendarPopoverState = {
  // Local selected offset (not confirmed until user clicks "Open selected")
  localSelectedOffset: number
  setLocalSelectedOffset: (offset: number) => void

  // Month/year dropdown states
  selectedMonth: string
  setSelectedMonth: (month: string) => void
  selectedYear: string
  setSelectedYear: (year: string) => void

  // Date picker highlighting
  selectedRange: { from: Date | undefined; to?: Date | undefined }
  setSelectedRange: (range: {
    from: Date | undefined
    to?: Date | undefined
  }) => void

  // Flag to track if store has been initialized
  isInitialized: boolean

  // Initialize function to sync with parent context when modal opens
  initialize: (
    currentPeriodOffset: number,
    currentPeriodStart: dayjs.Dayjs,
    effectivePayrollLength: number
  ) => void

  // Update month/year with new offset calculation
  updateMonthYear: (month: string, year: string, foundOffset: number) => void
}

export const useCalendarPopoverStore = create<CalendarPopoverState>(set => ({
  localSelectedOffset: 0,
  setLocalSelectedOffset: offset => set({ localSelectedOffset: offset }),

  selectedMonth: dayjs().format('M'),
  setSelectedMonth: month => set({ selectedMonth: month }),

  selectedYear: dayjs().format('YYYY'),
  setSelectedYear: year => set({ selectedYear: year }),

  selectedRange: { from: undefined, to: undefined },
  setSelectedRange: range => set({ selectedRange: range }),

  isInitialized: false,

  initialize: (
    currentPeriodOffset,
    currentPeriodStart,
    effectivePayrollLength
  ) =>
    set({
      localSelectedOffset: currentPeriodOffset,
      selectedMonth: currentPeriodStart.format('M'),
      selectedYear: currentPeriodStart.format('YYYY'),
      selectedRange: {
        from: currentPeriodStart.toDate(),
        to: currentPeriodStart
          .clone()
          .add(effectivePayrollLength - 1, 'days')
          .toDate()
      },
      isInitialized: true
    }),

  updateMonthYear: (month, year, foundOffset) =>
    set({
      selectedMonth: month,
      selectedYear: year,
      localSelectedOffset: foundOffset
    })
}))
