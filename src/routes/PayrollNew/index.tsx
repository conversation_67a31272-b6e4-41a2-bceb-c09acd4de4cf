import React from 'react'
import { useSelector } from 'react-redux'

// Types
import { RootState } from 'store/reducers'

// Styled Components
import {
  ContainerStyled,
  LoadingMessage,
  MainStyled
} from './styles/Payroll.styles'

// Utils
import { usePayrollIntegrations } from 'utils/hooks/usePayrollIntegrations'

// Hooks
import { usePayrollSettings } from './hooks/usePayrollSettings'
import { usePayrollIntegration } from './hooks/usePayrollIntegration'
import { usePayrollExport } from './hooks/usePayrollExport'

// Components
import PayrollContent from './PayrollContent'
import { PayrollHeader } from './components/PayrollHeader'
import { PayrollModals } from './components/PayrollModals'
import { PeriodProvider } from 'contexts/PeriodContext'

// Zustand stores
import { usePayrollUIStore } from './stores'

const Payroll: React.FC = () => {
  const currentCompany = useSelector((state: RootState) =>
    state.companies.find(company => company.key === state.currentCompanyId) ||
    { key: '', payrollStartingDay: 'Monday' } as any
  )

  // Zustand UI store for modal states
  const {
    showSettingsModal,
    showExportModal,
    openSettingsModal,
    closeSettingsModal,
    openExportModal,
    closeExportModal
  } = usePayrollUIStore()

  // Custom hooks for business logic
  const { attendanceSettings, setAttendanceSettings, isLoading: settingsLoading } = usePayrollSettings(
    currentCompany.key,
    currentCompany.payrollStartingDay
  )

  const { coNumber, integrationType, isLoading: integrationLoading } = usePayrollIntegration(currentCompany.key)

  const { handleExport } = usePayrollExport({
    coNumber,
    integrationType,
    onExportComplete: closeExportModal
  })

  // Employee integrations and payroll data
  usePayrollIntegrations(currentCompany.key)

  // Loading state
  if (settingsLoading || integrationLoading) {
    return (
      <ContainerStyled>
        <LoadingMessage>Loading payroll data...</LoadingMessage>
      </ContainerStyled>
    )
  }

  return (
    <PeriodProvider initialAttendanceSettings={attendanceSettings}>
      <ContainerStyled>
        <PayrollHeader
          onSettingsClick={openSettingsModal}
          onExportClick={openExportModal}
        />

        <MainStyled>
          <PayrollContent
            attendanceSettings={attendanceSettings}
            setAttendanceSettings={setAttendanceSettings}
          />
        </MainStyled>

        <PayrollModals
          showSettingsModal={showSettingsModal}
          showExportModal={showExportModal}
          onCloseSettings={closeSettingsModal}
          onCloseExport={closeExportModal}
          onExport={handleExport}
          attendanceSettings={attendanceSettings}
          setAttendanceSettings={setAttendanceSettings}
          integrationType={integrationType}
        />
      </ContainerStyled>
    </PeriodProvider>
  )
}

export default Payroll
